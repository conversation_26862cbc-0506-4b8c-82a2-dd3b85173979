// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts Account[]
  sessions Session[]
  notes    Note[]
  chats    Chat[]
  folders  Folder[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Application models
model Folder {
  id        String   @id @default(cuid())
  name      String
  color     String?  // Hex color for folder
  parentId  String?  // For nested folders
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String

  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent   Folder?  @relation("FolderHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  children Folder[] @relation("FolderHierarchy")
  notes    Note[]

  @@index([userId])
  @@index([parentId])
}

model Note {
  id          String   @id @default(cuid())
  title       String
  content     String   @db.Text
  summary     String?  @db.Text
  embedding   String?  @db.Text // JSON string of vector embedding
  tags        String[] // Array of tags
  isPublic    Boolean  @default(false)
  folderId    String?  // Optional folder assignment
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String

  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  folder Folder? @relation(fields: [folderId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([createdAt])
  @@index([folderId])
}

model Chat {
  id        String   @id @default(cuid())
  title     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@index([userId])
  @@index([createdAt])
}

model Message {
  id        String   @id @default(cuid())
  content   String   @db.Text
  role      String   // 'user' | 'assistant' | 'system'
  createdAt DateTime @default(now())
  chatId    String

  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)

  @@index([chatId])
  @@index([createdAt])
}
