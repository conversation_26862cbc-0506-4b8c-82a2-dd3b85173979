{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport GoogleProvider from 'next-auth/providers/google'\nimport GitHubProvider from 'next-auth/providers/github'\nimport { prisma } from './db'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || '',\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_ID || '',\n      clientSecret: process.env.GITHUB_SECRET || '',\n    }),\n  ],\n  callbacks: {\n    session: async ({ session, token }) => {\n      if (session?.user && token?.sub) {\n        session.user.id = token.sub\n      }\n      return session\n    },\n    jwt: async ({ user, token }) => {\n      if (user) {\n        token.uid = user.id\n      }\n      return token\n    },\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,kHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,SAAS,IAAI;YACnC,cAAc,QAAQ,GAAG,CAAC,aAAa,IAAI;QAC7C;KACD;IACD,WAAW;QACT,SAAS,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE;YAChC,IAAI,SAAS,QAAQ,OAAO,KAAK;gBAC/B,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;YAC7B;YACA,OAAO;QACT;QACA,KAAK,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;YACzB,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/app/api/search/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/db'\n\nexport async function GET(req: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.email) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { email: session.user.email }\n    })\n\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    const { searchParams } = new URL(req.url)\n    const query = searchParams.get('q') || ''\n    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || []\n    const type = searchParams.get('type') || 'notes' // 'notes' | 'chats' | 'all'\n\n    console.log('Search API called with:', { query, tags, type, userId: user.id })\n\n    let results: any = { notes: [], chats: [] }\n\n    // Search notes\n    if (type === 'notes' || type === 'all') {\n      const noteWhere: any = {\n        userId: user.id,\n        ...(query && {\n          OR: [\n            { title: { contains: query, mode: 'insensitive' } },\n            { content: { contains: query, mode: 'insensitive' } }\n          ]\n        }),\n        ...(tags.length > 0 && {\n          tags: { hasSome: tags }\n        })\n      }\n\n      const notes = await prisma.note.findMany({\n        where: noteWhere,\n        orderBy: { updatedAt: 'desc' },\n        take: 50\n      })\n\n      results.notes = notes\n    }\n\n    // Search chats\n    if (type === 'chats' || type === 'all') {\n      const chatWhere: any = {\n        userId: user.id,\n        ...(query && {\n          OR: [\n            { title: { contains: query, mode: 'insensitive' } },\n            {\n              messages: {\n                some: {\n                  content: { contains: query, mode: 'insensitive' }\n                }\n              }\n            }\n          ]\n        })\n      }\n\n      const chats = await prisma.chat.findMany({\n        where: chatWhere,\n        orderBy: { updatedAt: 'desc' },\n        include: {\n          messages: {\n            take: 1,\n            orderBy: { createdAt: 'desc' }\n          },\n          _count: {\n            select: { messages: true }\n          }\n        },\n        take: 50\n      })\n\n      results.chats = chats\n    }\n\n    // Get available tags for filtering\n    const availableTags = await prisma.note.findMany({\n      where: { userId: user.id },\n      select: { tags: true }\n    })\n\n    const allTags = [...new Set(availableTags.flatMap(note => note.tags))]\n\n    const response = {\n      ...results,\n      meta: {\n        query,\n        tags,\n        type,\n        availableTags: allTags\n      }\n    }\n\n    console.log('Search API response:', {\n      notesCount: results.notes.length,\n      chatsCount: results.chats.length,\n      availableTagsCount: allTags.length\n    })\n\n    return NextResponse.json(response)\n\n  } catch (error) {\n    console.error('Search error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;YAAC;QACrC;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;QACxC,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ;QACvC,MAAM,OAAO,aAAa,GAAG,CAAC,SAAS,MAAM,KAAK,OAAO,YAAY,EAAE;QACvE,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW,QAAQ,4BAA4B;;QAE7E,QAAQ,GAAG,CAAC,2BAA2B;YAAE;YAAO;YAAM;YAAM,QAAQ,KAAK,EAAE;QAAC;QAE5E,IAAI,UAAe;YAAE,OAAO,EAAE;YAAE,OAAO,EAAE;QAAC;QAE1C,eAAe;QACf,IAAI,SAAS,WAAW,SAAS,OAAO;YACtC,MAAM,YAAiB;gBACrB,QAAQ,KAAK,EAAE;gBACf,GAAI,SAAS;oBACX,IAAI;wBACF;4BAAE,OAAO;gCAAE,UAAU;gCAAO,MAAM;4BAAc;wBAAE;wBAClD;4BAAE,SAAS;gCAAE,UAAU;gCAAO,MAAM;4BAAc;wBAAE;qBACrD;gBACH,CAAC;gBACD,GAAI,KAAK,MAAM,GAAG,KAAK;oBACrB,MAAM;wBAAE,SAAS;oBAAK;gBACxB,CAAC;YACH;YAEA,MAAM,QAAQ,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvC,OAAO;gBACP,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,MAAM;YACR;YAEA,QAAQ,KAAK,GAAG;QAClB;QAEA,eAAe;QACf,IAAI,SAAS,WAAW,SAAS,OAAO;YACtC,MAAM,YAAiB;gBACrB,QAAQ,KAAK,EAAE;gBACf,GAAI,SAAS;oBACX,IAAI;wBACF;4BAAE,OAAO;gCAAE,UAAU;gCAAO,MAAM;4BAAc;wBAAE;wBAClD;4BACE,UAAU;gCACR,MAAM;oCACJ,SAAS;wCAAE,UAAU;wCAAO,MAAM;oCAAc;gCAClD;4BACF;wBACF;qBACD;gBACH,CAAC;YACH;YAEA,MAAM,QAAQ,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvC,OAAO;gBACP,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,SAAS;oBACP,UAAU;wBACR,MAAM;wBACN,SAAS;4BAAE,WAAW;wBAAO;oBAC/B;oBACA,QAAQ;wBACN,QAAQ;4BAAE,UAAU;wBAAK;oBAC3B;gBACF;gBACA,MAAM;YACR;YAEA,QAAQ,KAAK,GAAG;QAClB;QAEA,mCAAmC;QACnC,MAAM,gBAAgB,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;YACzB,QAAQ;gBAAE,MAAM;YAAK;QACvB;QAEA,MAAM,UAAU;eAAI,IAAI,IAAI,cAAc,OAAO,CAAC,CAAA,OAAQ,KAAK,IAAI;SAAG;QAEtE,MAAM,WAAW;YACf,GAAG,OAAO;YACV,MAAM;gBACJ;gBACA;gBACA;gBACA,eAAe;YACjB;QACF;QAEA,QAAQ,GAAG,CAAC,wBAAwB;YAClC,YAAY,QAAQ,KAAK,CAAC,MAAM;YAChC,YAAY,QAAQ,KAAK,CAAC,MAAM;YAChC,oBAAoB,QAAQ,MAAM;QACpC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAgB,GACpG;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}