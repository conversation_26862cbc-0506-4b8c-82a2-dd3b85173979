import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { Sidebar } from '@/components/dashboard/sidebar'
import { Header } from '@/components/dashboard/header'
import { MobileNavProvider } from '@/components/providers/mobile-nav-provider'
import { MobileSidebar } from '@/components/dashboard/mobile-sidebar'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  return (
    <MobileNavProvider>
      <div className="flex h-screen bg-gray-900">
        <Sidebar />
        <MobileSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <main className="flex-1 overflow-auto bg-gray-900">
            {children}
          </main>
        </div>
      </div>
    </MobileNavProvider>
  )
}
