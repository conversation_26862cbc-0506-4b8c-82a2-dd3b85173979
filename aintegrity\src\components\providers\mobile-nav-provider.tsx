'use client'

import { createContext, useContext, useState, ReactNode } from 'react'

interface MobileNavContextType {
  isMobileNavOpen: boolean
  setIsMobileNavOpen: (open: boolean) => void
  toggleMobileNav: () => void
}

const MobileNavContext = createContext<MobileNavContextType | undefined>(undefined)

export function MobileNavProvider({ children }: { children: ReactNode }) {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)

  const toggleMobileNav = () => {
    setIsMobileNavOpen(!isMobileNavOpen)
  }

  return (
    <MobileNavContext.Provider
      value={{
        isMobileNavOpen,
        setIsMobileNavOpen,
        toggleMobileNav,
      }}
    >
      {children}
    </MobileNavContext.Provider>
  )
}

export function useMobileNav() {
  const context = useContext(MobileNavContext)
  if (context === undefined) {
    throw new Error('useMobileNav must be used within a MobileNavProvider')
  }
  return context
}
