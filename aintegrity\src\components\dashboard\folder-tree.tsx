'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Folder, 
  FolderOpen, 
  Plus, 
  MoreHorizontal, 
  Edit2, 
  Trash2,
  ChevronRight,
  ChevronDown
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

interface FolderData {
  id: string
  name: string
  color?: string
  parentId?: string
  children: FolderData[]
  notes: Array<{
    id: string
    title: string
    createdAt: string
  }>
  _count: {
    notes: number
  }
}

interface FolderItemProps {
  folder: FolderData
  level: number
  onCreateFolder: (parentId: string) => void
  onDeleteFolder: (folderId: string) => void
}

function FolderItem({ folder, level, onCreateFolder, onDeleteFolder }: FolderItemProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editName, setEditName] = useState(folder.name)
  const pathname = usePathname()

  const isActive = pathname === `/dashboard/notes?folder=${folder.id}`
  const hasChildren = folder.children.length > 0

  const handleSaveEdit = async () => {
    // TODO: Implement folder rename API call
    setIsEditing(false)
  }

  return (
    <div>
      <div
        className={cn(
          'group flex items-center gap-1 py-1 px-2 rounded-md transition-colors',
          isActive ? 'bg-gray-700' : 'hover:bg-gray-700/50'
        )}
        style={{ paddingLeft: `${level * 12 + 8}px` }}
      >
        {hasChildren && (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        )}
        
        {!hasChildren && <div className="w-4" />}

        <div className="flex items-center gap-2 flex-1 min-w-0">
          {isExpanded ? (
            <FolderOpen className="h-4 w-4 text-blue-400 flex-shrink-0" />
          ) : (
            <Folder 
              className="h-4 w-4 flex-shrink-0" 
              style={{ color: folder.color || '#6b7280' }}
            />
          )}

          {isEditing ? (
            <Input
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleSaveEdit}
              onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit()}
              className="h-6 text-sm"
              autoFocus
            />
          ) : (
            <Link
              href={`/dashboard/notes?folder=${folder.id}`}
              className="flex-1 min-w-0"
            >
              <span className="text-sm text-white truncate block">
                {folder.name}
              </span>
            </Link>
          )}

          <span className="text-xs text-gray-400">
            {folder._count.notes}
          </span>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0"
            >
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onCreateFolder(folder.id)}>
              <Plus className="mr-2 h-4 w-4" />
              New Subfolder
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsEditing(true)}>
              <Edit2 className="mr-2 h-4 w-4" />
              Rename
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onDeleteFolder(folder.id)}
              className="text-red-400"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {isExpanded && hasChildren && (
        <div>
          {folder.children.map((child) => (
            <FolderItem
              key={child.id}
              folder={child}
              level={level + 1}
              onCreateFolder={onCreateFolder}
              onDeleteFolder={onDeleteFolder}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function FolderTree() {
  const [folders, setFolders] = useState<FolderData[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')

  const fetchFolders = async () => {
    try {
      const response = await fetch('/api/folders')
      if (response.ok) {
        const data = await response.json()
        // Filter to only show root folders (no parentId)
        const rootFolders = data.filter((folder: FolderData) => !folder.parentId)
        setFolders(rootFolders)
      }
    } catch (error) {
      console.error('Error fetching folders:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchFolders()
  }, [])

  const createFolder = async (parentId?: string) => {
    if (!newFolderName.trim()) return

    try {
      const response = await fetch('/api/folders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newFolderName,
          parentId,
          color: '#6b7280'
        })
      })

      if (response.ok) {
        setNewFolderName('')
        setIsCreating(false)
        fetchFolders()
      }
    } catch (error) {
      console.error('Error creating folder:', error)
    }
  }

  const deleteFolder = async (folderId: string) => {
    try {
      const response = await fetch(`/api/folders/${folderId}`, {
        method: 'DELETE'
      })
      if (response.ok) {
        fetchFolders()
      }
    } catch (error) {
      console.error('Error deleting folder:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-8 bg-gray-700 rounded animate-pulse" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-1">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-300">Folders</h3>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => setIsCreating(true)}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {isCreating && (
        <div className="flex gap-2 mb-2">
          <Input
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            placeholder="Folder name"
            className="h-8 text-sm"
            onKeyPress={(e) => e.key === 'Enter' && createFolder()}
            autoFocus
          />
          <Button
            size="sm"
            onClick={() => createFolder()}
            disabled={!newFolderName.trim()}
          >
            Add
          </Button>
        </div>
      )}

      {folders.length === 0 ? (
        <div className="text-center py-4">
          <Folder className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-400">No folders yet</p>
        </div>
      ) : (
        <div className="space-y-1">
          {folders.map((folder) => (
            <FolderItem
              key={folder.id}
              folder={folder}
              level={0}
              onCreateFolder={(parentId) => {
                setIsCreating(true)
                // TODO: Set parent context for new folder
              }}
              onDeleteFolder={deleteFolder}
            />
          ))}
        </div>
      )}
    </div>
  )
}
