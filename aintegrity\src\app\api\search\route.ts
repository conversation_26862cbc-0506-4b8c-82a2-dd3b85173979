import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { searchParams } = new URL(req.url)
    const query = searchParams.get('q') || ''
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || []
    const type = searchParams.get('type') || 'notes' // 'notes' | 'chats' | 'all'



    const results: { notes: unknown[]; chats: unknown[] } = { notes: [], chats: [] }

    // Search notes
    if (type === 'notes' || type === 'all') {
      const noteWhere = {
        userId: user.id,
        ...(query && {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { content: { contains: query, mode: 'insensitive' } }
          ]
        }),
        ...(tags.length > 0 && {
          tags: { hasSome: tags }
        })
      }

      const notes = await prisma.note.findMany({
        where: noteWhere,
        orderBy: { updatedAt: 'desc' },
        take: 50
      })

      results.notes = notes
    }

    // Search chats
    if (type === 'chats' || type === 'all') {
      const chatWhere = {
        userId: user.id,
        ...(query && {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            {
              messages: {
                some: {
                  content: { contains: query, mode: 'insensitive' }
                }
              }
            }
          ]
        })
      }

      const chats = await prisma.chat.findMany({
        where: chatWhere,
        orderBy: { updatedAt: 'desc' },
        include: {
          messages: {
            take: 1,
            orderBy: { createdAt: 'desc' }
          },
          _count: {
            select: { messages: true }
          }
        },
        take: 50
      })

      results.chats = chats
    }

    // Get available tags for filtering
    const availableTags = await prisma.note.findMany({
      where: { userId: user.id },
      select: { tags: true }
    })

    const allTags = [...new Set(availableTags.flatMap(note => note.tags))]

    return NextResponse.json({
      ...results,
      meta: {
        query,
        tags,
        type,
        availableTags: allTags
      }
    })

  } catch (error) {
    console.error('Search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
