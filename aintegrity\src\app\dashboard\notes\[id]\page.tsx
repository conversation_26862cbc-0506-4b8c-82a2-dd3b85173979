import { NoteEditor } from '@/components/dashboard/note-editor'

export default async function EditNotePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params

  return (
    <div className="p-4 lg:p-6">
      <div className="mb-4 lg:mb-6">
        <h1 className="text-xl lg:text-2xl font-bold text-white">Edit Note</h1>
        <p className="text-gray-400">Update your knowledge base</p>
      </div>
      <NoteEditor noteId={id} />
    </div>
  )
}
