'use client'

import { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Copy, Download, Maximize2 } from 'lucide-react'

interface MermaidProps {
  chart: string
  className?: string
}

export function Mermaid({ chart, className = '' }: MermaidProps) {
  const elementRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [svgContent, setSvgContent] = useState<string>('')

  useEffect(() => {
    const renderChart = async () => {
      if (!chart.trim()) return

      setIsLoading(true)
      setError(null)

      try {
        // Dynamic import to avoid SSR issues
        const mermaid = (await import('mermaid')).default

        // Initialize mermaid with dark theme
        mermaid.initialize({
          startOnLoad: false,
          theme: 'dark',
          themeVariables: {
            primaryColor: '#3b82f6',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#1e40af',
            lineColor: '#6b7280',
            sectionBkgColor: '#374151',
            altSectionBkgColor: '#4b5563',
            gridColor: '#6b7280',
            secondaryColor: '#1f2937',
            tertiaryColor: '#111827',
            background: '#111827',
            mainBkg: '#1f2937',
            secondBkg: '#374151',
            tertiaryBkg: '#4b5563'
          },
          fontFamily: 'ui-sans-serif, system-ui, sans-serif',
          fontSize: 14,
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true
          },
          sequence: {
            useMaxWidth: true,
            wrap: true
          },
          gantt: {
            useMaxWidth: true
          }
        })

        // Generate unique ID for this chart
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

        // Render the chart
        const { svg } = await mermaid.render(id, chart)
        setSvgContent(svg)

        if (elementRef.current) {
          elementRef.current.innerHTML = svg
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Mermaid rendering error:', err)
        setError(err instanceof Error ? err.message : 'Failed to render diagram')
        setIsLoading(false)
      }
    }

    renderChart()
  }, [chart])

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(chart)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const downloadSVG = () => {
    if (!svgContent) return

    const blob = new Blob([svgContent], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'mermaid-diagram.svg'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const openFullscreen = () => {
    if (!svgContent) return

    const newWindow = window.open('', '_blank')
    if (newWindow) {
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Mermaid Diagram</title>
            <style>
              body { 
                margin: 0; 
                padding: 20px; 
                background: #111827; 
                display: flex; 
                justify-content: center; 
                align-items: center; 
                min-height: 100vh; 
              }
              svg { max-width: 100%; height: auto; }
            </style>
          </head>
          <body>
            ${svgContent}
          </body>
        </html>
      `)
      newWindow.document.close()
    }
  }

  if (isLoading) {
    return (
      <div className={`border border-gray-600 rounded-lg p-4 bg-gray-800 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-400">Rendering diagram...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`border border-red-600 rounded-lg p-4 bg-red-900/20 ${className}`}>
        <div className="text-red-400 text-sm">
          <strong>Mermaid Error:</strong> {error}
        </div>
        <details className="mt-2">
          <summary className="text-red-300 cursor-pointer text-xs">Show diagram code</summary>
          <pre className="mt-2 text-xs bg-gray-800 p-2 rounded overflow-x-auto">
            <code>{chart}</code>
          </pre>
        </details>
      </div>
    )
  }

  return (
    <div className={`border border-gray-600 rounded-lg bg-gray-800 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b border-gray-600 bg-gray-700">
        <span className="text-xs text-gray-300 font-medium">Mermaid Diagram</span>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={copyToClipboard}
            className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            title="Copy diagram code"
          >
            <Copy className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={downloadSVG}
            className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            title="Download as SVG"
          >
            <Download className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={openFullscreen}
            className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            title="Open in new window"
          >
            <Maximize2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Diagram */}
      <div className="p-4 overflow-x-auto">
        <div 
          ref={elementRef} 
          className="flex justify-center items-center min-h-[100px]"
        />
      </div>
    </div>
  )
}
