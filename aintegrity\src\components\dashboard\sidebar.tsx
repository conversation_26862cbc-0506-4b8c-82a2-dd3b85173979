'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { MessageSquare, FileText, Plus, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { ChatHistory } from './chat-history'
import { FolderTree } from './folder-tree'

const navigation = [
  { name: 'Chat', href: '/dashboard', icon: MessageSquare },
  { name: 'Notes', href: '/dashboard/notes', icon: FileText },
  { name: 'Search', href: '/dashboard/search', icon: Search },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="hidden lg:flex w-64 bg-gray-800 shadow-sm border-r border-gray-700 flex-col">
      <div className="p-4">
        <h1 className="text-xl font-bold text-white">AIntegrity</h1>
      </div>

      <nav className="px-4 space-y-2 mb-6">
        {navigation.map((item) => {
          const isActive = pathname === item.href ||
            (item.href === '/dashboard' && pathname.startsWith('/dashboard/chat')) ||
            (item.href === '/dashboard/notes' && pathname.startsWith('/dashboard/notes'))
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-gray-700 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              )}
            >
              <item.icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          )
        })}
      </nav>

      <div className="flex-1 px-4 space-y-6 overflow-y-auto">
        <ChatHistory />
        <FolderTree />
      </div>

      <div className="p-4 border-t border-gray-700">
        <Button asChild className="w-full mb-2">
          <Link href="/dashboard/notes/new">
            <Plus className="mr-2 h-4 w-4" />
            New Note
          </Link>
        </Button>
        <Button asChild variant="outline" className="w-full">
          <Link href="/dashboard">
            <MessageSquare className="mr-2 h-4 w-4" />
            New Chat
          </Link>
        </Button>
      </div>
    </div>
  )
}
