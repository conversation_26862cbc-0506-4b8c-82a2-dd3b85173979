'use client'

import { useState, useEffect, useCallback } from 'react'
import { Search, Filter, X, Tag, Folder, MessageSquare, FileText } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
// Temporarily removed Select component - will add back after fixing dependencies
import { cn } from '@/lib/utils'
import Link from 'next/link'

interface SearchResult {
  notes: Array<{
    id: string
    title: string
    content: string
    tags: string[]
    createdAt: string
    updatedAt: string
  }>
  chats: Array<{
    id: string
    title: string
    createdAt: string
    updatedAt: string
    messages: Array<{
      content: string
    }>
    _count: {
      messages: number
    }
  }>
  meta: {
    query: string
    tags: string[]
    type: string
    availableTags: string[]
  }
}

export function SearchInterface() {
  const [query, setQuery] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [searchType, setSearchType] = useState<'all' | 'notes' | 'chats'>('all')
  const [results, setResults] = useState<SearchResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  // Removed folder fetching functionality

  const performSearch = useCallback(async () => {
    if (!query.trim() && selectedTags.length === 0) {
      setResults(null)
      return
    }

    setLoading(true)
    try {
      const params = new URLSearchParams({
        q: query,
        type: searchType,
        ...(selectedTags.length > 0 && { tags: selectedTags.join(',') })
      })

      const response = await fetch(`/api/search?${params}`)
      if (response.ok) {
        const data = await response.json()
        setResults(data)
      }
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setLoading(false)
    }
  }, [query, selectedTags, searchType])

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      performSearch()
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [performSearch])

  const addTag = (tag: string) => {
    if (!selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag])
    }
  }

  const removeTag = (tag: string) => {
    setSelectedTags(selectedTags.filter(t => t !== tag))
  }

  const clearFilters = () => {
    setQuery('')
    setSelectedTags([])
    setSearchType('all')
    setResults(null)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search notes and chats..."
          className="pl-10 pr-12"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="absolute right-1 top-1/2 transform -translate-y-1/2"
        >
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Search Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Type</label>
                <select
                  value={searchType}
                  onChange={(e) => setSearchType(e.target.value as any)}
                  className="w-full p-2 border border-gray-600 rounded-md bg-gray-800 text-white"
                >
                  <option value="all">All</option>
                  <option value="notes">Notes</option>
                  <option value="chats">Chats</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Tags</label>
                <div className="flex flex-wrap gap-1">
                  {results?.meta.availableTags.slice(0, 10).map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer text-xs"
                      onClick={() =>
                        selectedTags.includes(tag) ? removeTag(tag) : addTag(tag)
                      }
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {(selectedTags.length > 0 || query) && (
              <div className="flex items-center justify-between pt-2 border-t">
                <div className="flex flex-wrap gap-2">
                  {selectedTags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
                <Button variant="outline" size="sm" onClick={clearFilters}>
                  Clear all
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-sm text-gray-400 mt-2">Searching...</p>
        </div>
      )}

      {results && !loading && (
        <div className="space-y-6">
          {/* Notes Results */}
          {results.notes.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Notes ({results.notes.length})
              </h3>
              <div className="grid gap-3">
                {results.notes.map((note) => (
                  <Link key={note.id} href={`/dashboard/notes/${note.id}`}>
                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-white line-clamp-1">
                            {note.title}
                          </h4>
                        </div>
                        <p className="text-sm text-gray-300 line-clamp-2 mb-2">
                          {note.content.slice(0, 150)}...
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex flex-wrap gap-1">
                            {note.tags.slice(0, 3).map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {note.tags.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{note.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                          <span className="text-xs text-gray-400">
                            {formatDate(note.updatedAt)}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Chat Results */}
          {results.chats.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Chats ({results.chats.length})
              </h3>
              <div className="grid gap-3">
                {results.chats.map((chat) => (
                  <Link key={chat.id} href={`/dashboard/chat/${chat.id}`}>
                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-white line-clamp-1">
                            {chat.title || 'Untitled Chat'}
                          </h4>
                          <Badge variant="outline" className="text-xs">
                            {chat._count.messages} messages
                          </Badge>
                        </div>
                        {chat.messages[0] && (
                          <p className="text-sm text-gray-300 line-clamp-2 mb-2">
                            {chat.messages[0].content.slice(0, 150)}...
                          </p>
                        )}
                        <span className="text-xs text-gray-400">
                          {formatDate(chat.updatedAt)}
                        </span>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {results.notes.length === 0 && results.chats.length === 0 && (
            <div className="text-center py-8">
              <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No results found</h3>
              <p className="text-gray-400">Try adjusting your search terms or filters</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
