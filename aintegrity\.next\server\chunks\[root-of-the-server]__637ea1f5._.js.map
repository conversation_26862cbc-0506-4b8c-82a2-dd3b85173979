{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport GoogleProvider from 'next-auth/providers/google'\nimport GitHubProvider from 'next-auth/providers/github'\nimport { prisma } from './db'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || '',\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_ID || '',\n      clientSecret: process.env.GITHUB_SECRET || '',\n    }),\n  ],\n  callbacks: {\n    session: async ({ session, token }) => {\n      if (session?.user && token?.sub) {\n        session.user.id = token.sub\n      }\n      return session\n    },\n    jwt: async ({ user, token }) => {\n      if (user) {\n        token.uid = user.id\n      }\n      return token\n    },\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,kHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,SAAS,IAAI;YACnC,cAAc,QAAQ,GAAG,CAAC,aAAa,IAAI;QAC7C;KACD;IACD,WAAW;QACT,SAAS,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE;YAChC,IAAI,SAAS,QAAQ,OAAO,KAAK;gBAC/B,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;YAC7B;YACA,OAAO;QACT;QACA,KAAK,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;YACzB,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai'\n\nif (!process.env.OPENAI_API_KEY) {\n  throw new Error('Missing OPENAI_API_KEY environment variable')\n}\n\nexport const openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n})\n\nexport async function generateEmbedding(text: string): Promise<number[]> {\n  const response = await openai.embeddings.create({\n    model: 'text-embedding-3-small',\n    input: text,\n  })\n  \n  return response.data[0].embedding\n}\n\nexport async function generateChatCompletion(\n  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,\n  context?: string\n) {\n  const systemMessage = context \n    ? {\n        role: 'system' as const,\n        content: `You are a helpful AI assistant. You refuse to provide direct answers to academic questions and value academic integrity. You provide tutoring and guideance and always cite your sources.\n        Use the following context from the user's notes to inform your responses when relevant:\\n\\n${context}`\n      }\n    : {\n        role: 'system' as const,\n        content: 'You are a helpful AI assistant.'\n      }\n\n  const response = await openai.chat.completions.create({\n    model: 'gpt-o4-mini',\n    messages: [systemMessage, ...messages],\n    temperature: 0.7,\n    max_tokens: 2000,\n  })\n\n  return response.choices[0]?.message?.content || ''\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC/B,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEO,eAAe,kBAAkB,IAAY;IAClD,MAAM,WAAW,MAAM,OAAO,UAAU,CAAC,MAAM,CAAC;QAC9C,OAAO;QACP,OAAO;IACT;IAEA,OAAO,SAAS,IAAI,CAAC,EAAE,CAAC,SAAS;AACnC;AAEO,eAAe,uBACpB,QAA2E,EAC3E,OAAgB;IAEhB,MAAM,gBAAgB,UAClB;QACE,MAAM;QACN,SAAS,CAAC;mGACiF,EAAE,SAAS;IACxG,IACA;QACE,MAAM;QACN,SAAS;IACX;IAEJ,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,OAAO;QACP,UAAU;YAAC;eAAkB;SAAS;QACtC,aAAa;QACb,YAAY;IACd;IAEA,OAAO,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;AAClD", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/app/api/notes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/db'\nimport { generateEmbedding } from '@/lib/openai'\n\nexport async function GET(req: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n\n    if (!session?.user?.email) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { email: session.user.email }\n    })\n\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    const whereClause: any = { userId: user.id }\n\n    const notes = await prisma.note.findMany({\n      where: whereClause,\n      orderBy: { updatedAt: 'desc' },\n      select: {\n        id: true,\n        title: true,\n        content: true,\n        summary: true,\n        tags: true,\n        isPublic: true,\n        createdAt: true,\n        updatedAt: true,\n      }\n    })\n\n    return NextResponse.json(notes)\n\n  } catch (error) {\n    console.error('Notes GET error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(req: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.email) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { title, content, tags = [], isPublic = false } = await req.json()\n\n    if (!title || !content) {\n      return NextResponse.json(\n        { error: 'Title and content are required' },\n        { status: 400 }\n      )\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { email: session.user.email }\n    })\n\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    // Generate embedding for the note\n    let embedding = null\n    try {\n      const embeddingVector = await generateEmbedding(`${title} ${content}`)\n      embedding = JSON.stringify(embeddingVector)\n    } catch (error) {\n      console.error('Error generating embedding:', error)\n    }\n\n    const note = await prisma.note.create({\n      data: {\n        title,\n        content,\n        tags,\n        isPublic,\n\n        embedding,\n        userId: user.id,\n      }\n    })\n\n    return NextResponse.json(note)\n\n  } catch (error) {\n    console.error('Notes POST error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;YAAC;QACrC;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,MAAM,cAAmB;YAAE,QAAQ,KAAK,EAAE;QAAC;QAE3C,MAAM,QAAQ,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,OAAO;YACP,SAAS;gBAAE,WAAW;YAAO;YAC7B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,WAAW,KAAK,EAAE,GAAG,MAAM,IAAI,IAAI;QAEtE,IAAI,CAAC,SAAS,CAAC,SAAS;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;YAAC;QACrC;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,kCAAkC;QAClC,IAAI,YAAY;QAChB,IAAI;YACF,MAAM,kBAAkB,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,MAAM,CAAC,EAAE,SAAS;YACrE,YAAY,KAAK,SAAS,CAAC;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA;gBACA;gBAEA;gBACA,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}