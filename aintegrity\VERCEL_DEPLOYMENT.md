# Vercel Deployment Guide for AIntegrity

This guide will help you deploy your AIntegrity application to Vercel with all the necessary configurations.

## 🚀 Quick Deployment Steps

### 1. Prerequisites
- GitHub repository with your code
- Vercel account (free tier works)
- Production database (Supabase recommended)
- OpenAI API key

### 2. Database Setup (Choose One)

#### Option A: Supabase (Recommended)
1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Wait for setup completion
4. Go to Settings > Database
5. Copy the connection string
6. Replace `[YOUR-PASSWORD]` with your actual password

#### Option B: Neon Database
1. Go to [neon.tech](https://neon.tech)
2. Create new project
3. Copy the connection string

### 3. Deploy to Vercel

#### Step 1: Connect Repository
1. Go to [vercel.com](https://vercel.com)
2. Click "New Project"
3. Import your GitHub repository
4. Select the `aintegrity` folder as root directory

#### Step 2: Configure Environment Variables
Add these in Vercel dashboard (Settings > Environment Variables):

```bash
# Database (Required)
DATABASE_URL="your-production-database-url"

# Authentication (Required)
NEXTAUTH_URL="https://your-app-name.vercel.app"
NEXTAUTH_SECRET="your-secure-secret-32-chars"

# OpenAI (Required)
OPENAI_API_KEY="sk-your-openai-api-key"

# OAuth Providers (Optional but recommended)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"

# Supabase (if using Supabase)
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
```

#### Step 3: Deploy
1. Click "Deploy"
2. Wait for build to complete (3-5 minutes)
3. Visit your deployed application

### 4. Post-Deployment Setup

#### Database Migration
After successful deployment, run database migration:
```bash
# In your local terminal
npx prisma db push --schema=./aintegrity/prisma/schema.prisma
```

#### OAuth Configuration
Update your OAuth applications with the new production URLs:

**Google OAuth:**
- Authorized redirect URIs: `https://your-app-name.vercel.app/api/auth/callback/google`

**GitHub OAuth:**
- Authorization callback URL: `https://your-app-name.vercel.app/api/auth/callback/github`

### 5. Testing Your Deployment

1. Visit your Vercel URL
2. Test sign-in functionality
3. Create a note
4. Try the AI chat feature
5. Verify all features work correctly

## 🔧 Troubleshooting

### Common Issues

#### Prisma Client Error
If you see "Prisma Client not generated" error:
- The build script now includes `npx prisma generate`
- The postinstall script automatically generates Prisma client
- Vercel.json is configured for proper Prisma handling

#### Database Connection Issues
- Verify DATABASE_URL is correct
- Ensure database allows connections from Vercel IPs
- Check if SSL is required (add `?sslmode=require` to connection string)

#### OAuth Redirect Errors
- Update OAuth app settings with production URLs
- Verify NEXTAUTH_URL matches your Vercel domain

### Build Logs
Check Vercel build logs for specific error messages:
1. Go to Vercel dashboard
2. Click on your project
3. Go to "Functions" or "Deployments"
4. Click on latest deployment
5. Check build logs

## 🎯 Environment Variables Reference

### Required Variables
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - 32-character secret for NextAuth
- `OPENAI_API_KEY` - Your OpenAI API key

### Optional Variables
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - Google OAuth
- `GITHUB_ID` & `GITHUB_SECRET` - GitHub OAuth
- `NEXT_PUBLIC_SUPABASE_URL` & `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase

### Generate NEXTAUTH_SECRET
```bash
openssl rand -base64 32
```

## 🔄 Continuous Deployment

Once set up, Vercel will automatically deploy when you push to your main branch:
1. Make changes locally
2. Commit and push to GitHub
3. Vercel automatically builds and deploys
4. Check deployment status in Vercel dashboard

## 📞 Support

If you encounter issues:
1. Check Vercel build logs
2. Verify environment variables
3. Test database connection
4. Review OAuth configuration
5. Check the troubleshooting section above
