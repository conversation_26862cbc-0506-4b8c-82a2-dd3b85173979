import OpenAI from 'openai'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function generateEmbedding(text: string): Promise<number[]> {
  const response = await openai.embeddings.create({
    model: 'text-embedding-3-small',
    input: text,
  })
  
  return response.data[0].embedding
}

export async function generateChatCompletion(
  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
  context?: string
) {
  const systemMessage = context 
    ? {
        role: 'system' as const,
        content: `You are a helpful AI assistant. You refuse to provide direct answers to academic questions and value academic integrity. You provide tutoring and guideance and always cite your sources.
        Use the following context from the user's notes to inform your responses when relevant:\n\n${context}`
      }
    : {
        role: 'system' as const,
        content: 'You are a helpful AI assistant.'
      }

  const response = await openai.chat.completions.create({
    model: 'gpt-o4-mini',
    messages: [systemMessage, ...messages],
    temperature: 0.7,
    max_tokens: 2000,
  })

  return response.choices[0]?.message?.content || ''
}
