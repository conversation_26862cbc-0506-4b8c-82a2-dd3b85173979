'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { MessageSquare, FileText, Plus, X, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useMobileNav } from '@/components/providers/mobile-nav-provider'
import { useEffect } from 'react'

const navigation = [
  { name: 'Chat', href: '/dashboard', icon: MessageSquare },
  { name: 'Notes', href: '/dashboard/notes', icon: FileText },
  { name: 'Search', href: '/dashboard/search', icon: Search },
]

export function MobileSidebar() {
  const pathname = usePathname()
  const { isMobileNavOpen, setIsMobileNavOpen } = useMobileNav()

  // Close mobile nav when route changes
  useEffect(() => {
    setIsMobileNavOpen(false)
  }, [pathname, setIsMobileNavOpen])

  // Prevent body scroll when mobile nav is open
  useEffect(() => {
    if (isMobileNavOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isMobileNavOpen])

  if (!isMobileNavOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40 bg-black/50 lg:hidden"
        onClick={() => setIsMobileNavOpen(false)}
      />
      
      {/* Mobile Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 shadow-xl border-r border-gray-700 flex flex-col lg:hidden">
        {/* Header with close button */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h1 className="text-xl font-bold text-white">AIntegrity</h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMobileNavOpen(false)}
            className="text-gray-300 hover:text-white"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 px-4 py-4 space-y-2">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-gray-700 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                )}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            )
          })}
        </nav>

        {/* New Note Button */}
        <div className="p-4 border-t border-gray-700">
          <Button asChild className="w-full">
            <Link href="/dashboard/notes/new">
              <Plus className="mr-2 h-4 w-4" />
              New Note
            </Link>
          </Button>
        </div>
      </div>
    </>
  )
}
