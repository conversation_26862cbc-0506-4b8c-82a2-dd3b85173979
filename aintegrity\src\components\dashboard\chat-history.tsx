'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { MessageSquare, Plus, MoreHorizontal, Trash2, Edit2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

interface Chat {
  id: string
  title: string
  createdAt: string
  updatedAt: string
  messages: Array<{
    id: string
    content: string
    role: string
    createdAt: string
  }>
  _count: {
    messages: number
  }
}

export function ChatHistory() {
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)
  const pathname = usePathname()

  const fetchChats = async () => {
    try {
      const response = await fetch('/api/chats')
      if (response.ok) {
        const data = await response.json()
        setChats(data)
      }
    } catch (error) {
      console.error('Error fetching chats:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchChats()
  }, [])

  const deleteChat = async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: 'DELETE'
      })
      if (response.ok) {
        setChats(chats.filter(chat => chat.id !== chatId))
      }
    } catch (error) {
      console.error('Error deleting chat:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return 'Today'
    if (diffDays === 2) return 'Yesterday'
    if (diffDays <= 7) return `${diffDays - 1} days ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-12 bg-gray-700 rounded animate-pulse" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-300">Recent Chats</h3>
        <Button asChild size="sm" variant="ghost">
          <Link href="/dashboard">
            <Plus className="h-4 w-4" />
          </Link>
        </Button>
      </div>

      {chats.length === 0 ? (
        <div className="text-center py-8">
          <MessageSquare className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-400">No chats yet</p>
          <Button asChild size="sm" className="mt-2">
            <Link href="/dashboard">Start chatting</Link>
          </Button>
        </div>
      ) : (
        <div className="space-y-1">
          {chats.map((chat) => {
            const isActive = pathname === `/dashboard/chat/${chat.id}`
            const lastMessage = chat.messages[0]
            
            return (
              <div
                key={chat.id}
                className={cn(
                  'group relative rounded-md transition-colors',
                  isActive ? 'bg-gray-700' : 'hover:bg-gray-700/50'
                )}
              >
                <Link
                  href={`/dashboard/chat/${chat.id}`}
                  className="block p-3 pr-10"
                >
                  <div className="flex items-start gap-2">
                    <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-white truncate">
                        {chat.title || 'Untitled Chat'}
                      </p>
                      {lastMessage && (
                        <p className="text-xs text-gray-400 truncate mt-1">
                          {lastMessage.content.slice(0, 50)}...
                        </p>
                      )}
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-gray-500">
                          {formatDate(chat.updatedAt)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {chat._count.messages} messages
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-1 top-1 opacity-0 group-hover:opacity-100 h-8 w-8 p-0"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Edit2 className="mr-2 h-4 w-4" />
                      Rename
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => deleteChat(chat.id)}
                      className="text-red-400"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
